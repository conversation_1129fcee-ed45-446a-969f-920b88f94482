This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.10.4)  8 JUL 2025 22:30
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**2024年度中央预算和财政收支审计工作报告.tex
(./2024年度中央预算和财政收支审计工作报告.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(c:/texlive/2024/texmf-dist/tex/latex/ctex/ctexart.cls
(c:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexbackend.cfg
File: ctexbackend.cfg 2022/07/14 v2.5.10 Backend configuration file (CTEX)
)
(c:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 

(c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count184
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count185
\g__pdf_backend_annotation_int=\count186
\g__pdf_backend_link_int=\count187
))
Document Class: ctexart 2022/07/14 v2.5.10 Chinese adapter for class article (C
TEX)
(c:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)
(c:/texlive/2024/texmf-dist/tex/latex/ctex/ctexpatch.sty
Package: ctexpatch 2022/07/14 v2.5.10 Patching commands (CTEX)
)
(c:/texlive/2024/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX

(c:/texlive/2024/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
\l__ctex_tmp_int=\count188
\l__ctex_tmp_box=\box52
\l__ctex_tmp_dim=\dimen140
\g__ctex_section_depth_int=\count189
\g__ctex_font_size_int=\count190

(c:/texlive/2024/texmf-dist/tex/latex/ctex/config/ctexopts.cfg
File: ctexopts.cfg 2022/07/14 v2.5.10 Option configuration file (CTEX)
)
(c:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(c:/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count191
\c@section=\count192
\c@subsection=\count193
\c@subsubsection=\count194
\c@paragraph=\count195
\c@subparagraph=\count196
\c@figure=\count197
\c@table=\count198
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen141
)
(c:/texlive/2024/texmf-dist/tex/latex/ctex/engine/ctex-engine-xetex.def
File: ctex-engine-xetex.def 2022/07/14 v2.5.10 XeLaTeX adapter (CTEX)

(c:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX


! LaTeX Error: File `xtemplate.sty' not found.

Type X to quit or <RETURN> to proceed,
or enter new name. (Default extension: sty)

Enter file name: 
! Emergency stop.
<read *> 
         
l.80 \tl_new:N
               \l__xeCJK_tmp_tl 
*** (cannot \read from terminal in nonstop modes)

 
Here is how much of TeX's memory you used:
 950 strings out of 474773
 23416 string characters out of 5761289
 1917842 words of memory out of 5000000
 23157 multiletter control sequences out of 15000+600000
 559177 words of font info for 39 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 65i,0n,79p,253b,224s stack positions out of 10000i,1000n,20000p,200000b,200000s
No pages of output.
