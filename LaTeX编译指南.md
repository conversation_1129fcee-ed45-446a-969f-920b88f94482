# LaTeX文档编译指南

## 📋 文档信息
- **文件名**: `数字中国建设评论.tex`
- **文档类型**: 中文学术论文
- **编译引擎**: XeLaTeX (推荐) 或 pdfLaTeX

## 🔧 编译环境要求

### 必需软件
1. **TeX发行版**:
   - Windows: TeX Live 或 MiKTeX
   - macOS: MacTeX
   - Linux: TeX Live

2. **中文字体**:
   - 宋体 (SimSun)
   - 黑体 (SimHei)

### 推荐编辑器
- TeXstudio
- Overleaf (在线)
- VS Code + LaTeX Workshop
- TeXworks

## 🚀 编译步骤

### 方法一：命令行编译
```bash
# 使用XeLaTeX编译（推荐）
xelatex 数字中国建设评论.tex
xelatex 数字中国建设评论.tex  # 再次编译确保交叉引用正确

# 或使用pdfLaTeX编译
pdflatex 数字中国建设评论.tex
pdflatex 数字中国建设评论.tex
```

### 方法二：编辑器编译
1. 在LaTeX编辑器中打开 `数字中国建设评论.tex`
2. 设置编译引擎为 XeLaTeX
3. 点击编译按钮
4. 查看生成的PDF文件

## 📐 格式特性说明

### 页面设置
- **纸张**: A4 (210×297mm)
- **页边距**: 上下2.54cm，左右3.17cm
- **行距**: 1.5倍行距
- **字体**: 正文宋体12pt，标题黑体

### 标题层次
- **主标题**: 居中，黑体，大号字体
- **一级标题**: section，黑体，大字体
- **正文**: 宋体，12pt，首行缩进2字符

### 特殊处理
- **百分号转义**: 所有%符号已转义为\%
- **中文支持**: 使用ctex宏包
- **页眉页脚**: 自动生成页码和标题

## 🎨 自定义选项

### 修改字体
```latex
% 在导言区修改字体设置
\setCJKmainfont{你的字体名称}  % 正文字体
\setCJKsansfont{你的字体名称}  % 标题字体
```

### 调整页边距
```latex
% 修改geometry包设置
\geometry{
    top=3cm,      % 上边距
    bottom=3cm,   % 下边距
    left=2.5cm,   % 左边距
    right=2.5cm   % 右边距
}
```

### 修改行距
```latex
% 在导言区修改
\doublespacing    % 双倍行距
\singlespacing    % 单倍行距
\onehalfspacing   % 1.5倍行距（当前设置）
```

## 🔍 常见问题解决

### 1. 中文显示问题
**问题**: 中文字符显示为方框或乱码
**解决**: 
- 确保使用XeLaTeX编译
- 检查系统是否安装了宋体和黑体
- 确认文件编码为UTF-8

### 2. 编译错误
**问题**: 编译时出现错误
**解决**:
- 检查LaTeX语法是否正确
- 确认所有宏包都已安装
- 查看错误日志定位问题

### 3. 字体缺失
**问题**: 提示字体未找到
**解决**:
- Windows: 安装宋体和黑体字体
- 或修改字体设置为系统已有字体

### 4. 页码问题
**问题**: 页码显示异常
**解决**:
- 多次编译确保交叉引用正确
- 检查fancyhdr包设置

## 📊 输出效果

编译成功后将生成：
- **PDF文件**: `数字中国建设评论.pdf`
- **辅助文件**: .aux, .log, .toc等（可删除）

### 预期效果
- ✅ 专业的学术论文格式
- ✅ 清晰的标题层次
- ✅ 规范的中文排版
- ✅ 适合打印的页面布局

## 💡 使用建议

1. **首次编译**: 建议使用TeXstudio等集成环境
2. **字体问题**: 如遇字体问题，可替换为系统默认中文字体
3. **在线编译**: 可使用Overleaf在线编译，无需本地安装
4. **版本控制**: 建议使用Git管理LaTeX源文件

## 📝 后续修改

如需修改内容：
1. 直接编辑 `.tex` 文件
2. 重新编译生成新的PDF
3. 所有格式设置会自动应用

这个LaTeX文档完全保持了您原始内容的一字不漏，同时提供了专业的学术论文格式！
