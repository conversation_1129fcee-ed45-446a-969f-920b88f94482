This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.10.4)  27 JUL 2025 09:51
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**数字中国建设评论_修复版.tex
(./数字中国建设评论_修复版.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(c:/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(c:/texlive/2024/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count184
\c@section=\count185
\c@subsection=\count186
\c@subsubsection=\count187
\c@paragraph=\count188
\c@subparagraph=\count189
\c@figure=\count190
\c@table=\count191
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(c:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.sty
(c:/texlive/2024/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
(c:/texlive/2024/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 

(c:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count192
\l__pdf_internal_box=\box51
\g__pdf_backend_object_int=\count193
\g__pdf_backend_annotation_int=\count194
\g__pdf_backend_link_int=\count195
))
Package: xparse 2024-02-18 L3 Experimental document command parser
)
Package: fontspec 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTeX

(c:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/02/13 v2.9a Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count196
\l__fontspec_language_int=\count197
\l__fontspec_strnum_int=\count198
\l__fontspec_tmp_int=\count199
\l__fontspec_tmpa_int=\count266
\l__fontspec_tmpb_int=\count267
\l__fontspec_tmpc_int=\count268
\l__fontspec_em_int=\count269
\l__fontspec_emdef_int=\count270
\l__fontspec_strong_int=\count271
\l__fontspec_strongdef_int=\count272
\l__fontspec_tmpa_dim=\dimen141
\l__fontspec_tmpb_dim=\dimen142
\l__fontspec_tmpc_dim=\dimen143

(c:/texlive/2024/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(c:/texlive/2024/texmf-dist/tex/latex/fontspec/fontspec.cfg)))
(c:/texlive/2024/texmf-dist/tex/xelatex/xecjk/xeCJK.sty
Package: xeCJK 2022/08/05 v3.9.1 Typesetting CJK scripts with XeLaTeX

(c:/texlive/2024/texmf-dist/tex/latex/ctex/ctexhook.sty
Package: ctexhook 2022/07/14 v2.5.10 Document and package hooks (CTEX)
)

! LaTeX Error: File `xtemplate.sty' not found.

Type X to quit or <RETURN> to proceed,
or enter new name. (Default extension: sty)

Enter file name: 